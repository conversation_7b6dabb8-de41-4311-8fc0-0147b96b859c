ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x28 (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2820,len:0x56c
load:0x403c8700,len:0x4
load:0x403c8704,len:0xc2c
load:0x403cb700,len:0x2e58
entry 0x403c890c
I (36) octal_psram: vendor id    : 0x0d (AP)
I (36) octal_psram: dev id       : 0x02 (generation 3)
I (36) octal_psram: density      : 0x03 (64 Mbit)
I (38) octal_psram: good-die     : 0x01 (Pass)
I (42) octal_psram: Latency      : 0x01 (Fixed)
I (46) octal_psram: VCC          : 0x01 (3V)
I (50) octal_psram: SRF          : 0x01 (Fast Refresh)
I (55) octal_psram: BurstType    : 0x01 (Hybrid Wrap)
I (60) octal_psram: BurstLen     : 0x01 (32 Byte)
I (64) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)
I (70) octal_psram: DriveStrength: 0x00 (1/1)
I (74) MSPI Timing: PSRAM timing tuning index: 4
I (78) esp_psram: Found 8MB PSRAM device
I (82) esp_psram: Speed: 80MHz
I (85) cpu_start: Multicore app
I (99) cpu_start: Pro cpu start user code
I (99) cpu_start: cpu freq: 240000000 Hz
I (99) app_init: Application information:
I (99) app_init: Project name:     xiaozhi
I (103) app_init: App version:      1.7.6
I (106) app_init: Compile time:     Jun 29 2025 14:57:20
I (112) app_init: ELF file SHA256:  c7cfb558f...
I (116) app_init: ESP-IDF:          v5.4.1-dirty
I (120) efuse_init: Min chip rev:     v0.0
I (124) efuse_init: Max chip rev:     v0.99 
I (128) efuse_init: Chip rev:         v0.2
I (132) heap_init: Initializing. RAM available for dynamic allocation:
I (138) heap_init: At 3FCAD350 len 0003C3C0 (240 KiB): RAM
I (143) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM
I (148) heap_init: At 600FE11C len 00001EBC (7 KiB): RTCRAM
I (154) esp_psram: Adding pool of 8192K of PSRAM memory to heap allocator
I (161) spi_flash: detected chip: generic
I (164) spi_flash: flash io: qio
I (168) sleep_gpio: Configure to isolate all GPIO pins in sleep state
I (173) sleep_gpio: Enable automatic switching of GPIO sleep configuration
I (180) main_task: Started on CPU0
I (210) esp_psram: Reserving pool of 64K of internal memory for DMA/internal allocations
I (210) main_task: Calling app_main()
I (220) BackgroundTask: background_task started
I (220) Board: UUID=24032b21-086e-4df0-a70e-1e27e24c171f SKU=bread-compact-wifi
I (220) gpio: GPIO[0]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (230) button: IoT Button Version: 4.1.3
I (230) gpio: GPIO[47]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (240) gpio: GPIO[40]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (250) gpio: GPIO[39]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (260) CompactWifiBoard: Install SSD1306 driver
I (260) CompactWifiBoard: SSD1306 driver installed
I (270) CompactWifiBoard: Turning display on
I (370) Display: Power management not supported
I (370) OledDisplay: Initialize LVGL
I (370) LVGL: Starting LVGL task
I (370) OledDisplay: Adding OLED display
I (390) gpio: GPIO[18]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (390) MCP: Add tool: self.lamp.get_state
I (390) MCP: Add tool: self.lamp.turn_on
I (400) MCP: Add tool: self.lamp.turn_off
I (400) Application: STATE: starting
I (400) NoAudioCodec: Simplex channels created
I (410) Application: Audio processor detected, setting opus encoder complexity to 5
I (410) AudioCodec: Set input enable to true
I (420) AudioCodec: Set output enable to true
I (420) AudioCodec: Audio codec started
I (430) Application: STATE: configuring
I (430) DnsServer: Starting DNS server
I (430) pp: pp rom version: e7ae62f
I (440) net80211: net80211 rom version: e7ae62f
I (460) wifi:wifi driver task: 3fcdc50c, prio:23, stack:6144, core=0
I (460) wifi:wifi firmware version: 79fa3f41ba
I (460) wifi:wifi certification version: v7.0
I (460) wifi:config NVS flash: enabled
I (460) wifi:config nano formatting: enabled
I (470) wifi:Init data frame dynamic rx buffer num: 8
I (470) wifi:Init dynamic rx mgmt buffer num: 5
I (470) wifi:Init management short buffer num: 32
I (480) wifi:Init dynamic tx buffer num: 32
I (480) wifi:Init static tx FG buffer num: 2
I (490) wifi:Init static rx buffer size: 1600
I (490) wifi:Init static rx buffer num: 6
I (500) wifi:Init dynamic rx buffer num: 8
I (500) wifi_init: rx ba win: 6
I (500) wifi_init: accept mbox: 6
I (500) wifi_init: tcpip mbox: 32
I (510) wifi_init: udp mbox: 6
I (510) wifi_init: tcp mbox: 6
I (510) wifi_init: tcp tx win: 5760
I (520) wifi_init: tcp rx win: 5760
I (520) wifi_init: tcp mss: 1440
I (530) wifi:Set ps type: 0, coexist: 0

I (530) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11
I (560) wifi:mode : sta (b4:3a:45:a1:e4:64) + softAP (b4:3a:45:a1:e4:65)
I (560) wifi:enable tsf
I (560) wifi:Total power save buffer number: 16
I (560) wifi:Init max length of beacon: 752/752
I (570) wifi:Init max length of beacon: 752/752
I (570) WifiConfigurationAp: Access Point started with SSID Xiaozhi-E465
I (570) esp_netif_lwip: DHCP server started on interface WIFI_AP_DEF with IP: ***********
I (590) WifiConfigurationAp: Web server started
W (590) Application: Alert 配网模式: 手机连接热点 Xiaozhi-E465，浏览器访问 http://***********

 []
I (620) WifiBoard: Free internal: 120563 minimal internal: 120563
I (640) Application: Resampling audio from 16000 to 24000
I (640) OpusResampler: Resampler configured with input sample rate 16000 and output sample rate 24000
I (10440) SystemInfo: free sram: 125079 minimal sram: 120071
I (10620) WifiBoard: Free internal: 124999 minimal internal: 120071
I (20440) SystemInfo: free sram: 124931 minimal sram: 120071
I (20620) WifiBoard: Free internal: 124851 minimal internal: 120071
I (30440) SystemInfo: free sram: 124939 minimal sram: 120071
I (30620) WifiBoard: Free internal: 124859 minimal internal: 120071
I (40440) SystemInfo: free sram: 124531 minimal sram: 120071
I (40460) wifi:new:<1,0>, old:<1,1>, ap:<1,0>, sta:<0,0>, prof:1, snd_ch_cfg:0x0
I (40460) wifi:scan in process, ignore set current channel
I (40460) wifi:station: ee:20:c6:13:db:10 join, AID=1, bgn, 20
I (40460) WifiConfigurationAp: Station ee:20:c6:13:db:10 joined, AID=1
I (40620) WifiBoard: Free internal: 124175 minimal internal: 120071
I (43380) esp_netif_lwip: DHCP server assigned IP to a client, IP is: ***********
I (45440) DnsServer: Sending DNS response to ***********
I (45440) DnsServer: Sending DNS response to ***********
I (47210) DnsServer: Sending DNS response to ***********
I (47210) DnsServer: Sending DNS response to ***********
I (48190) wifi:<ba-add>idx:2 (ifx:1, ee:20:c6:13:db:10), tid:0, ssn:41, winSize:64
I (50430) SystemInfo: free sram: 124347 minimal sram: 115255
I (50620) WifiBoard: Free internal: 124347 minimal internal: 115255
I (51560) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -26, Authmode: 4
I (51560) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -29, Authmode: 4
I (51570) WifiConfigurationAp: SSID: ASUS2G, RSSI: -50, Authmode: 3
I (51570) WifiConfigurationAp: SSID: CU_faUE, RSSI: -67, Authmode: 3
I (51580) WifiConfigurationAp: SSID: Tenda_EB05C0, RSSI: -74, Authmode: 3
I (51580) WifiConfigurationAp: SSID: ChinaNet-NWyX, RSSI: -82, Authmode: 3
I (56750) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -27, Authmode: 4
I (56750) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -29, Authmode: 4
I (56760) WifiConfigurationAp: SSID: ASUS2G, RSSI: -50, Authmode: 3
I (56760) WifiConfigurationAp: SSID: CU_faUE, RSSI: -66, Authmode: 3
I (56770) WifiConfigurationAp: SSID: Tenda_EB05C0, RSSI: -73, Authmode: 3
I (56770) WifiConfigurationAp: SSID: HUAWEI-R1CS7C, RSSI: -79, Authmode: 3
I (56780) WifiConfigurationAp: SSID: ChinaNet-NWyX, RSSI: -82, Authmode: 3
I (56790) WifiConfigurationAp: SSID: ZZZ, RSSI: -83, Authmode: 3
I (56790) WifiConfigurationAp: SSID: SGM OnStar 3080, RSSI: -86, Authmode: 3
I (60430) SystemInfo: free sram: 123923 minimal sram: 115255
I (60620) WifiBoard: Free internal: 123927 minimal internal: 115255
I (61880) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -27, Authmode: 4
I (61890) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -29, Authmode: 4
I (61890) WifiConfigurationAp: SSID: ASUS2G, RSSI: -50, Authmode: 3
I (61890) WifiConfigurationAp: SSID: CU_faUE, RSSI: -66, Authmode: 3
I (61900) WifiConfigurationAp: SSID: Tenda_EB05C0, RSSI: -73, Authmode: 3
I (61900) WifiConfigurationAp: SSID: HUAWEI-R1CS7C, RSSI: -79, Authmode: 3
I (61910) WifiConfigurationAp: SSID: ChinaNet-NWyX, RSSI: -82, Authmode: 3
I (61920) WifiConfigurationAp: SSID: ZZZ, RSSI: -83, Authmode: 3
I (61920) WifiConfigurationAp: SSID: SGM OnStar 3080, RSSI: -86, Authmode: 3
I (67230) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -27, Authmode: 4
I (67240) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -29, Authmode: 4
I (67240) WifiConfigurationAp: SSID: ASUS2G, RSSI: -50, Authmode: 3
I (67240) WifiConfigurationAp: SSID: CU_faUE, RSSI: -66, Authmode: 3
I (67250) WifiConfigurationAp: SSID: Tenda_EB05C0, RSSI: -73, Authmode: 3
I (67250) WifiConfigurationAp: SSID: HUAWEI-R1CS7C, RSSI: -79, Authmode: 3
I (67260) WifiConfigurationAp: SSID: ChinaNet-NWyX, RSSI: -82, Authmode: 3
I (67270) WifiConfigurationAp: SSID: ZZZ, RSSI: -83, Authmode: 3
I (67270) WifiConfigurationAp: SSID: SGM OnStar 3080, RSSI: -86, Authmode: 3
W (68350) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2
I (68370) WifiConfigurationAp: Connecting to WiFi TPMLPLINK
I (70430) SystemInfo: free sram: 123231 minimal sram: 115255
I (70620) WifiBoard: Free internal: 123231 minimal internal: 115255
I (71550) wifi:new:<1,1>, old:<1,0>, ap:<1,0>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (71550) wifi:state: init -> auth (0xb0)
I (71560) wifi:state: auth -> assoc (0x0)
I (71570) wifi:state: assoc -> run (0x10)
I (71580) wifi:connected with TPMLPLINK, aid = 1, channel 1, 40U, bssid = 68:77:24:b5:c4:cf
I (71590) wifi:security: WPA2-PSK, phy: bgn, rssi: -26
I (71600) wifi:pm start, type: 0

I (71600) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us
I (71600) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000
I (71610) WifiConfigurationAp: Connected to WiFi TPMLPLINK
I (71620) wifi:state: run -> init (0x0)
I (71630) wifi:pm stop, total sleep time: lu us / lu us

I (71630) wifi:new:<1,0>, old:<1,1>, ap:<1,0>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (71630) WifiConfigurationAp: Save SSID TPMLPLINK 9
I (75030) WifiConfigurationAp: Rebooting...
I (75440) wifi:station: ee:20:c6:13:db:10 leave, AID = 1, reason = 2, bss_flags is 33721459, bss:0x3c2a016c
I (75440) wifi:new:<1,0>, old:<1,0>, ap:<1,0>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (75440) wifi:<ba-del>idx:2, tid:0
I (75450) WifiConfigurationAp: Station ee:20:c6:13:db:10 left, AID=1
I (75560) wifi:flush txq
I (75560) wifi:stop sw txq
I (75560) wifi:lmac stop hw txq
SP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x28 (SPI_FAST_FLASH_BOOT)
Saved PC:0x40379ead
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2820,len:0x56c
load:0x403c8700,len:0x4
load:0x403c8704,len:0xc2c
load:0x403cb700,len:0x2e58
entry 0x403c890c
I (40) octal_psram: vendor id    : 0x0d (AP)
I (40) octal_psram: dev id       : 0x02 (generation 3)
I (41) octal_psram: density      : 0x03 (64 Mbit)
I (42) octal_psram: good-die     : 0x01 (Pass)
I (47) octal_psram: Latency      : 0x01 (Fixed)
I (51) octal_psram: VCC          : 0x01 (3V)
I (55) octal_psram: SRF          : 0x01 (Fast Refresh)
I (60) octal_psram: BurstType    : 0x01 (Hybrid Wrap)
I (64) octal_psram: BurstLen     : 0x01 (32 Byte)
I (69) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)
I (74) octal_psram: DriveStrength: 0x00 (1/1)
I (79) MSPI Timing: PSRAM timing tuning index: 5
I (82) esp_psram: Found 8MB PSRAM device
I (86) esp_psram: Speed: 80MHz
I (89) cpu_start: Multicore app
I (103) cpu_start: Pro cpu start user code
I (103) cpu_start: cpu freq: 240000000 Hz
I (104) app_init: Application information:
I (104) app_init: Project name:     xiaozhi
I (107) app_init: App version:      1.7.6
I (111) app_init: Compile time:     Jun 29 2025 14:57:20
I (116) app_init: ELF file SHA256:  c7cfb558f...
I (121) app_init: ESP-IDF:          v5.4.1-dirty
I (125) efuse_init: Min chip rev:     v0.0
I (129) efuse_init: Max chip rev:     v0.99 
I (133) efuse_init: Chip rev:         v0.2
I (137) heap_init: Initializing. RAM available for dynamic allocation:
I (143) heap_init: At 3FCAD350 len 0003C3C0 (240 KiB): RAM
I (148) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM
I (153) heap_init: At 600FE11C len 00001EBC (7 KiB): RTCRAM
I (159) esp_psram: Adding pool of 8192K of PSRAM memory to heap allocator
I (166) spi_flash: detected chip: generic
I (169) spi_flash: flash io: qio
I (173) sleep_gpio: Configure to isolate all GPIO pins in sleep state
I (178) sleep_gpio: Enable automatic switching of GPIO sleep configuration
I (185) main_task: Started on CPU0
I (215) esp_psram: Reserving pool of 64K of internal memory for DMA/internal allocations
I (215) main_task: Calling app_main()
I (235) BackgroundTask: background_task started
I (235) Board: UUID=24032b21-086e-4df0-a70e-1e27e24c171f SKU=bread-compact-wifi
I (235) gpio: GPIO[0]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (245) button: IoT Button Version: 4.1.3
I (245) gpio: GPIO[47]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (255) gpio: GPIO[40]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (265) gpio: GPIO[39]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (275) CompactWifiBoard: Install SSD1306 driver
I (275) CompactWifiBoard: SSD1306 driver installed
I (285) CompactWifiBoard: Turning display on
I (385) Display: Power management not supported
I (385) OledDisplay: Initialize LVGL
I (385) LVGL: Starting LVGL task
I (385) OledDisplay: Adding OLED display
I (405) gpio: GPIO[18]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (405) MCP: Add tool: self.lamp.get_state
I (405) MCP: Add tool: self.lamp.turn_on
I (415) MCP: Add tool: self.lamp.turn_off
I (415) Application: STATE: starting
I (415) NoAudioCodec: Simplex channels created
I (425) Application: Audio processor detected, setting opus encoder complexity to 5
I (425) AudioCodec: Set input enable to true
I (435) AudioCodec: Set output enable to true
I (435) AudioCodec: Audio codec started
I (445) pp: pp rom version: e7ae62f
I (445) net80211: net80211 rom version: e7ae62f
I (455) wifi:wifi driver task: 3fcdb244, prio:23, stack:6144, core=0
I (455) wifi:wifi firmware version: 79fa3f41ba
I (455) wifi:wifi certification version: v7.0
I (465) wifi:config NVS flash: disabled
I (465) wifi:config nano formatting: enabled
I (475) wifi:Init data frame dynamic rx buffer num: 8
I (475) wifi:Init dynamic rx mgmt buffer num: 5
I (475) wifi:Init management short buffer num: 32
I (485) wifi:Init dynamic tx buffer num: 32
I (485) wifi:Init static tx FG buffer num: 2
I (495) wifi:Init static rx buffer size: 1600
I (495) wifi:Init static rx buffer num: 6
I (495) wifi:Init dynamic rx buffer num: 8
I (505) wifi_init: rx ba win: 6
I (505) wifi_init: accept mbox: 6
I (505) wifi_init: tcpip mbox: 32
I (515) wifi_init: udp mbox: 6
I (515) wifi_init: tcp mbox: 6
I (515) wifi_init: tcp tx win: 5760
I (525) wifi_init: tcp rx win: 5760
I (525) wifi_init: tcp mss: 1440
I (525) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11
I (565) wifi:mode : sta (b4:3a:45:a1:e4:64)
I (575) wifi:enable tsf
I (2975) wifi: Found AP: TPMLPLINK, BSSID: 68:77:24:b5:c4:cf, RSSI: -26, Channel: 1, Authmode: 4
I (2975) wifi: Found AP: TPMLPLINK, BSSID: f4:84:8d:1a:28:b3, RSSI: -31, Channel: 1, Authmode: 4
W (2985) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2
I (3335) wifi:new:<1,1>, old:<1,0>, ap:<255,255>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (3335) wifi:state: init -> auth (0xb0)
I (3335) wifi:state: auth -> assoc (0x0)
I (3345) wifi:state: assoc -> run (0x10)
I (3355) wifi:connected with TPMLPLINK, aid = 1, channel 1, 40U, bssid = 68:77:24:b5:c4:cf
I (3355) wifi:security: WPA2-PSK, phy: bgn, rssi: -28
I (3355) wifi:pm start, type: 1

I (3355) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us
I (3365) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000
I (3425) wifi:AP's beacon interval = 102400 us, DTIM period = 1
I (4055) wifi:<ba-add>idx:0 (ifx:0, 68:77:24:b5:c4:cf), tid:0, ssn:0, winSize:64
I (5255) wifi: Got IP: ***********03
I (5255) esp_netif_handlers: sta ip: ***********03, mask: *************, gw: ***********
I (5265) Application: STATE: activating
I (5265) Ota: Current version: 1.7.6
I (5275) EspHttp: Opening HTTP connection to https://xiaozhi.winxtec.cn:8002/xiaozhi/ota/
E (5565) esp-tls-mbedtls: mbedtls_ssl_handshake returned -0x7200
I (5565) esp-tls-mbedtls: Certificate verified.
E (5565) esp-tls: Failed to open new connection
E (5565) transport_base: Failed to open a new connection
E (5575) HTTP_CLIENT: Connection failed, sock < 0
E (5575) EspHttp: Failed to perform HTTP request: ESP_ERR_HTTP_CONNECT
E (5585) Ota: Failed to open HTTP connection
W (5585) Application: Alert 错误: 检查新版本失败，将在 10 秒后重试：https://xiaozhi.winxtec.cn:8002/xiaozhi/ota/ [sad]
W (5615) Application: Check new version failed, retry in 10 seconds (1/10)
I (5635) Application: Resampling audio from 16000 to 24000
I (5635) OpusResampler: Resampler configured with input sample rate 16000 and output sample rate 24000
I (14445) SystemInfo: free sram: 136843 minimal sram: 133079
I (15615) Ota: Current version: 1.7.6
I (15625) EspHttp: Opening HTTP connection to https://xiaozhi.winxtec.cn:8002/xiaozhi/ota/
E (15695) esp-tls-mbedtls: mbedtls_ssl_handshake returned -0x7200
I (15695) esp-tls-mbedtls: Certificate verified.
E (15695) esp-tls: Failed to open new connection
E (15695) transport_base: Failed to open a new connection
E (15705) HTTP_CLIENT: Connection failed, sock < 0
E (15705) EspHttp: Failed to perform HTTP request: ESP_ERR_HTTP_CONNECT
E (15715) Ota: Failed to open HTTP connection
W (15715) Application: Alert 错误: 检查新版本失败，将在 20 秒后重试：https://xiaozhi.winxtec.cn:8002/xiaozhi/ota/ [sad]
W (15745) Application: Check new version failed, retry in 20 seconds (2/10)
I (24445) SystemInfo: free sram: 136843 minimal sram: 132515
I (34445) SystemInfo: free sram: 136843 minimal sram: 132515
I (35745) Ota: Current version: 1.7.6
I (35745) EspHttp: Opening HTTP connection to https://xiaozhi.winxtec.cn:8002/xiaozhi/ota/
E (35845) esp-tls-mbedtls: mbedtls_ssl_handshake returned -0x7200
I (35845) esp-tls-mbedtls: Certificate verified.
E (35845) esp-tls: Failed to open new connection
E (35855) transport_base: Failed to open a new connection
E (35855) HTTP_CLIENT: Connection failed, sock < 0
E (35855) EspHttp: Failed to perform HTTP request: ESP_ERR_HTTP_CONNECT
E (35865) Ota: Failed to open HTTP connection
W (35875) Application: Alert 错误: 检查新版本失败，将在 40 秒后重试：https://xiaozhi.winxtec.cn:8002/xiaozhi/ota/ [sad]
W (35895) Application: Check new version failed, retry in 40 seconds (3/10)
I (44455) SystemInfo: free sram: 136855 minimal sram: 132063
I (54455) SystemInfo: free sram: 136815 minimal sram: 132063
I (64455) SystemInfo: free sram: 136815 minimal sram: 132063
I (74455) SystemInfo: free sram: 136855 minimal sram: 132063
I (75895) Ota: Current version: 1.7.6
I (75895) EspHttp: Opening HTTP connection to https://xiaozhi.winxtec.cn:8002/xiaozhi/ota/
