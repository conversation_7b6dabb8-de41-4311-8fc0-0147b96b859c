# WinXtec Compact WiFi Board

## 概述

WinXtec Compact WiFi 开发板是基于 ESP32-S3 的紧凑型 WiFi 语音交互开发板，继承了 `bread-compact-wifi` 的设计，但采用了更先进的音频方案。

## 硬件特性

### 主控芯片
- **ESP32-S3**: 双核 Xtensa LX7 处理器
- **WiFi**: 802.11 b/g/n
- **蓝牙**: Bluetooth 5.0 LE

### 音频系统
- **功放**: NS4168 (I2S数字功放)
  - 高保真音频输出
  - 低失真，高效率
  - 支持单声道/立体声输出
- **麦克风**: MEMS3526 (PDM数字麦克风)
  - 高信噪比 MEMS 麦克风
  - PDM 数字输出
  - 优秀的语音识别性能

### 显示屏
- **OLED**: SSD1306/SH1106
- **分辨率**: 128x32 或 128x64
- **接口**: I2C

### 用户交互
- **按键**: 4个按键 (启动、触摸、音量+、音量-)
- **LED**: WS2812 RGB LED 状态指示
- **触摸**: 按住说话模式

### 物联网控制
- **灯具控制**: GPIO 18 (MCP协议)
- **协议支持**: 小智协议 / MCP协议

## GPIO 配置

### 音频接口
| GPIO | 功能 | 描述 |
|------|------|------|
| GPIO 7 | SPK_DOUT | NS4168 数据输出 |
| GPIO 15 | SPK_BCLK | NS4168 位时钟 |
| GPIO 16 | SPK_LRCK | NS4168 左右声道时钟 |
| GPIO 5 | MIC_CLK | MEMS3526 PDM 时钟 |
| GPIO 6 | MIC_DIN | MEMS3526 PDM 数据 |

### 用户交互
| GPIO | 功能 | 描述 |
|------|------|------|
| GPIO 0 | BOOT_BTN | 启动按键/聊天切换 |
| GPIO 47 | TOUCH_BTN | 触摸按键 (按住说话) |
| GPIO 40 | VOL_UP | 音量增加按键 |
| GPIO 39 | VOL_DOWN | 音量减少按键 |
| GPIO 48 | LED | WS2812 RGB LED |

### 显示和控制
| GPIO | 功能 | 描述 |
|------|------|------|
| GPIO 41 | DISPLAY_SDA | OLED I2C 数据线 |
| GPIO 42 | DISPLAY_SCL | OLED I2C 时钟线 |
| GPIO 18 | LAMP_CTRL | 灯具控制输出 |

## 音频特性

### 采样率配置
- **麦克风输入**: 16kHz (语音质量)
- **功放输出**: 24kHz (音频质量)

### I2S通道分配
- **I2S0**: PDM麦克风输入 (MEMS3526) - PDM功能仅在I2S0上支持
- **I2S1**: I2S功放输出 (NS4168) - 标准I2S输出

### NS4168 功放特性
- **输出功率**: 3W @ 4Ω
- **THD+N**: <1% @ 1W
- **信噪比**: >90dB
- **工作电压**: 2.5V-5.5V

### MEMS3526 麦克风特性
- **灵敏度**: -26dBFS
- **信噪比**: 64dB SPL
- **频率响应**: 100Hz-10kHz
- **PDM 频率**: 1.024MHz-3.072MHz

## 软件特性

### 音频编解码器
- **WinxtecAudioCodec**: 专用音频编解码器类
- **混合模式**: I2S输出 + PDM输入
- **音量控制**: 软件音量调节 (0-100)
- **状态管理**: 独立的输入输出控制

### 设备状态指示
- **启动中**: 蓝色快速闪烁
- **WiFi配置**: 蓝色慢速闪烁
- **空闲**: LED关闭
- **监听**: 红色常亮
- **语音检测**: 红色高亮

## 编译配置

### 支持的构建
1. **winxtec-compact-wifi**: 128x32 OLED
2. **winxtec-compact-wifi-128x64**: 128x64 OLED

### 编译命令
```bash
# 128x32 版本
idf.py set-target esp32s3
idf.py -D BOARD_TYPE=winxtec-compact-wifi build

# 128x64 版本  
idf.py set-target esp32s3
idf.py -D BOARD_TYPE=winxtec-compact-wifi-128x64 build
```

## 硬件连接

### NS4168 功放模块
```
ESP32-S3        NS4168
GPIO 15   -->   BCLK
GPIO 16   -->   LRCK  
GPIO 7    -->   DIN
3.3V      -->   VDD
GND       -->   GND
```

### MEMS3526 麦克风模块
```
ESP32-S3        MEMS3526
GPIO 5    -->   CLK
GPIO 6    -->   DATA
3.3V      -->   VDD
GND       -->   GND
```

### OLED 显示屏
```
ESP32-S3        OLED
GPIO 41   -->   SDA
GPIO 42   -->   SCL
3.3V      -->   VCC
GND       -->   GND
```

## 应用场景

- **智能音箱**: 高质量音频播放和语音识别
- **语音助手**: 支持唤醒词和语音交互
- **物联网控制**: 通过语音控制智能设备
- **教学演示**: 嵌入式音频处理教学

## 技术优势

1. **高质量音频**: NS4168 提供专业级音频输出
2. **优秀拾音**: MEMS3526 确保清晰的语音输入
3. **低功耗设计**: 优化的电源管理
4. **易于开发**: 完整的软件框架支持
5. **扩展性强**: 丰富的 GPIO 资源

## 版本历史

- **v1.0**: 初始版本，基于 bread-compact-wifi 设计
- 支持 NS4168 + MEMS3526 音频方案
- 完整的 MCP 协议支持
