




























ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x29 (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x490
load:0x403c8700,len:0x4
load:0x403c8704,len:0xb08
load:0x403cb700,len:0x2bb4
entry 0x403c88f4
I (91) octal_psram: vendor id    : 0x0d (AP)
I (91) octal_psram: dev id       : 0x02 (generation 3)
I (91) octal_psram: density      : 0x03 (64 Mbit)
I (93) octal_psram: good-die     : 0x01 (Pass)
I (97) octal_psram: Latency      : 0x01 (Fixed)
I (102) octal_psram: VCC          : 0x01 (3V)
I (106) octal_psram: SRF          : 0x01 (Fast Refresh)
I (111) octal_psram: BurstType    : 0x01 (Hybrid Wrap)
I (115) octal_psram: BurstLen     : 0x01 (32 Byte)
I (120) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)
I (125) octal_psram: DriveStrength: 0x00 (1/1)
I (130) MSPI Timing: PSRAM timing tuning index: 4
I (134) esp_psram: Found 8MB PSRAM device
I (137) esp_psram: Speed: 80MHz
I (140) cpu_start: Multicore app
I (155) cpu_start: Pro cpu start user code
I (155) cpu_start: cpu freq: 240000000 Hz
I (155) app_init: Application information:
I (155) app_init: Project name:     xiaozhi
I (159) app_init: App version:      1.7.6
I (163) app_init: Compile time:     Jun 29 2025 19:55:51
I (168) app_init: ELF file SHA256:  f989af9f9...
I (172) app_init: ESP-IDF:          v5.4.1-dirty
I (177) efuse_init: Min chip rev:     v0.0
I (181) efuse_init: Max chip rev:     v0.99 
I (185) efuse_init: Chip rev:         v0.2
I (188) heap_init: Initializing. RAM available for dynamic allocation:
I (195) heap_init: At 3FCAD350 len 0003C3C0 (240 KiB): RAM
I (200) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM
I (205) heap_init: At 600FE11C len 00001EBC (7 KiB): RTCRAM
I (210) esp_psram: Adding pool of 8192K of PSRAM memory to heap allocator
I (218) spi_flash: detected chip: generic
I (221) spi_flash: flash io: dio
I (225) sleep_gpio: Configure to isolate all GPIO pins in sleep state
I (230) sleep_gpio: Enable automatic switching of GPIO sleep configuration
I (237) main_task: Started on CPU0
I (267) esp_psram: Reserving pool of 64K of internal memory for DMA/internal allocations
I (267) main_task: Calling app_main()
I (267) BackgroundTask: background_task started
I (287) Board: UUID=7861a53f-5fb7-4e9c-a4b9-f6dd989e0a2f SKU=bread-compact-wifi
I (287) gpio: GPIO[0]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (287) button: IoT Button Version: 4.1.3
I (287) gpio: GPIO[47]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (297) gpio: GPIO[40]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (307) gpio: GPIO[39]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (317) CompactWifiBoard: Install SSD1306 driver
I (317) CompactWifiBoard: SSD1306 driver installed
I (327) CompactWifiBoard: Turning display on
I (427) Display: Power management not supported
I (427) OledDisplay: Initialize LVGL
I (427) LVGL: Starting LVGL task
I (427) OledDisplay: Adding OLED display
I (447) gpio: GPIO[18]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (447) MCP: Add tool: self.lamp.get_state
I (447) MCP: Add tool: self.lamp.turn_on
I (457) MCP: Add tool: self.lamp.turn_off
I (457) Application: STATE: starting
I (467) NoAudioCodec: Simplex channels created
I (467) Application: Audio processor not detected, setting opus encoder complexity to 0
I (477) AudioCodec: Set input enable to true
I (477) AudioCodec: Set output enable to true
I (477) AudioCodec: Audio codec started
I (487) Application: STATE: configuring
I (487) DnsServer: Starting DNS server
I (497) pp: pp rom version: e7ae62f
I (497) net80211: net80211 rom version: e7ae62f
I (507) wifi:wifi driver task: 3fcdc57c, prio:23, stack:6144, core=0
I (507) wifi:wifi firmware version: 79fa3f41ba
I (507) wifi:wifi certification version: v7.0
I (517) wifi:config NVS flash: enabled
I (517) wifi:config nano formatting: enabled
I (517) wifi:Init data frame dynamic rx buffer num: 8
I (527) wifi:Init dynamic rx mgmt buffer num: 5
I (527) wifi:Init management short buffer num: 32
I (537) wifi:Init dynamic tx buffer num: 32
I (537) wifi:Init static tx FG buffer num: 2
I (547) wifi:Init static rx buffer size: 1600
I (547) wifi:Init static rx buffer num: 6
I (547) wifi:Init dynamic rx buffer num: 8
I (557) wifi_init: rx ba win: 6
I (557) wifi_init: accept mbox: 6
I (557) wifi_init: tcpip mbox: 32
I (567) wifi_init: udp mbox: 6
I (567) wifi_init: tcp mbox: 6
I (567) wifi_init: tcp tx win: 5760
I (567) wifi_init: tcp rx win: 5760
I (577) wifi_init: tcp mss: 1440
I (587) wifi:Set ps type: 0, coexist: 0

I (587) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11
W (587) phy_init: failed to load RF calibration data (0x1102), falling back to full calibration
I (627) phy_init: Saving new calibration data due to checksum failure or outdated calibration data, mode(2)
I (637) wifi:mode : sta (b4:3a:45:a1:e4:64) + softAP (b4:3a:45:a1:e4:65)
I (637) wifi:enable tsf
I (637) wifi:Total power save buffer number: 16
I (637) wifi:Init max length of beacon: 752/752
I (647) wifi:Init max length of beacon: 752/752
I (647) WifiConfigurationAp: Access Point started with SSID Xiaozhi-E465
I (647) esp_netif_lwip: DHCP server started on interface WIFI_AP_DEF with IP: ***********
I (667) WifiConfigurationAp: Web server started
W (667) Application: Alert 配网模式: 手机连接热点 Xiaozhi-E465，浏览器访问 http://***********

 []
I (697) WifiBoard: Free internal: 120627 minimal internal: 120627
I (727) Application: Resampling audio from 16000 to 24000
I (727) OpusResampler: Resampler configured with input sample rate 16000 and output sample rate 24000
I (10487) SystemInfo: free sram: 125147 minimal sram: 120263
I (10697) WifiBoard: Free internal: 125147 minimal internal: 120263
I (14097) wifi:new:<1,0>, old:<1,1>, ap:<1,0>, sta:<0,0>, prof:1, snd_ch_cfg:0x0
I (14097) wifi:scan in process, ignore set current channel
I (14097) wifi:station: ee:20:c6:13:db:10 join, AID=1, bgn, 20
I (14097) WifiConfigurationAp: Station ee:20:c6:13:db:10 joined, AID=1
I (14237) esp_netif_lwip: DHCP server assigned IP to a client, IP is: ***********
I (14837) wifi:I (14837) DnsServer: Sending DNS response to ***********
<ba-add>idx:2 (ifx:1, ee:20:c6:13:db:10), tid:0, ssn:0, winSize:64
I (14837) DnsServer: Sending DNS response to ***********
I (17407) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -21, Authmode: 4
I (17407) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -31, Authmode: 4
I (17407) WifiConfigurationAp: SSID: ASUS2G, RSSI: -48, Authmode: 3
I (17417) WifiConfigurationAp: SSID: Tenda_EB05C0, RSSI: -62, Authmode: 3
I (17417) WifiConfigurationAp: SSID: CU_faUE, RSSI: -69, Authmode: 3
I (17427) WifiConfigurationAp: SSID: HUAWEI-R1CS7C, RSSI: -81, Authmode: 3
I (17437) WifiConfigurationAp: SSID: ChinaNet-NWyX, RSSI: -86, Authmode: 3
I (17537) wifi:<ba-add>idx:3 (ifx:1, ee:20:c6:13:db:10), tid:1, ssn:0, winSize:64
I (19487) DnsServer: Sending DNS response to ***********
I (19487) DnsServer: Sending DNS response to ***********
I (19507) DnsServer: Sending DNS response to ***********
I (19507) DnsServer: Sending DNS response to ***********
I (20497) SystemInfo: free sram: 123535 minimal sram: 115219
I (20697) WifiBoard: Free internal: 123451 minimal internal: 115219
I (22567) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -21, Authmode: 4
I (22577) WifiConfigurationAp: SSID: TPMLPLINK, RSSI: -31, Authmode: 4
I (22577) WifiConfigurationAp: SSID: ASUS2G, RSSI: -48, Authmode: 3
I (22577) WifiConfigurationAp: SSID: Tenda_EB05C0, RSSI: -62, Authmode: 3
I (22587) WifiConfigurationAp: SSID: CU_faUE, RSSI: -69, Authmode: 3
I (22597) WifiConfigurationAp: SSID: HUAWEI-R1CS7C, RSSI: -81, Authmode: 3
I (22597) WifiConfigurationAp: SSID: ChinaNet-NWyX, RSSI: -86, Authmode: 3
W (25817) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2
I (25837) WifiConfigurationAp: Connecting to WiFi TPMLPLINK
I (29027) wifi:new:<1,1>, old:<1,0>, ap:<1,0>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (29027) wifi:state: init -> auth (0xb0)
I (29037) wifi:state: auth -> assoc (0x0)
I (29037) wifi:state: assoc -> run (0x10)
I (29057) wifi:connected with TPMLPLINK, aid = 1, channel 1, 40U, bssid = 68:77:24:b5:c4:cf
I (29057) wifi:security: WPA2-PSK, phy: bgn, rssi: -23
I (29067) wifi:pm start, type: 0

I (29067) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us
I (29067) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000
I (29077) WifiConfigurationAp: Connected to WiFi TPMLPLINK
I (29087) wifi:state: run -> init (0x0)
I (29097) wifi:pm stop, total sleep time: lu us / lu us

I (29097) wifi:new:<1,0>, old:<1,1>, ap:<1,0>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (29097) WifiConfigurationAp: Save SSID TPMLPLINK 9
I (30487) SystemInfo: free sram: 122843 minimal sram: 115219
I (30697) WifiBoard: Free internal: 122843 minimal internal: 115219
I (32307) WifiConfigurationAp: Rebooting...
I (32317) wifi:station: ee:20:c6:13:db:10 leave, AID = 1, reason = 8, bss_flags is 33721443, bss:0x3c29fc98
I (32317) wifi:new:<1,0>, old:<1,0>, ap:<1,0>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (32317) wifi:<ba-del>idx:2, tid:0
I (32327) wifi:<ba-del>idx:3, tid:1
I (32327) WifiConfigurationAp: Station ee:20:c6:13:db:10 left, AID=1
I (32717) wifi:flush txq
I (32717) wifi:stop sw txq
I (32717) wifi:lmac stop hw txq
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0xc (RTC_SW_CPU_RST),boot:0x29 (SPI_FAST_FLASH_BOOT)
Saved PC:0x40379ead
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x490
load:0x403c8700,len:0x4
load:0x403c8704,len:0xb08
load:0x403cb700,len:0x2bb4
entry 0x403c88f4
I (44) octal_psram: vendor id    : 0x0d (AP)
I (44) octal_psram: dev id       : 0x02 (generation 3)
I (45) octal_psram: density      : 0x03 (64 Mbit)
I (46) octal_psram: good-die     : 0x01 (Pass)
I (50) octal_psram: Latency      : 0x01 (Fixed)
I (55) octal_psram: VCC          : 0x01 (3V)
I (59) octal_psram: SRF          : 0x01 (Fast Refresh)
I (64) octal_psram: BurstType    : 0x01 (Hybrid Wrap)
I (68) octal_psram: BurstLen     : 0x01 (32 Byte)
I (73) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)
I (78) octal_psram: DriveStrength: 0x00 (1/1)
I (83) MSPI Timing: PSRAM timing tuning index: 4
I (86) esp_psram: Found 8MB PSRAM device
I (90) esp_psram: Speed: 80MHz
I (93) cpu_start: Multicore app
I (108) cpu_start: Pro cpu start user code
I (108) cpu_start: cpu freq: 240000000 Hz
I (108) app_init: Application information:
I (108) app_init: Project name:     xiaozhi
I (112) app_init: App version:      1.7.6
I (115) app_init: Compile time:     Jun 29 2025 19:55:51
I (120) app_init: ELF file SHA256:  f989af9f9...
I (125) app_init: ESP-IDF:          v5.4.1-dirty
I (129) efuse_init: Min chip rev:     v0.0
I (133) efuse_init: Max chip rev:     v0.99 
I (137) efuse_init: Chip rev:         v0.2
I (141) heap_init: Initializing. RAM available for dynamic allocation:
I (147) heap_init: At 3FCAD350 len 0003C3C0 (240 KiB): RAM
I (152) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM
I (157) heap_init: At 600FE11C len 00001EBC (7 KiB): RTCRAM
I (163) esp_psram: Adding pool of 8192K of PSRAM memory to heap allocator
I (170) spi_flash: detected chip: generic
I (173) spi_flash: flash io: dio
I (177) sleep_gpio: Configure to isolate all GPIO pins in sleep state
I (182) sleep_gpio: Enable automatic switching of GPIO sleep configuration
I (189) main_task: Started on CPU0
I (219) esp_psram: Reserving pool of 64K of internal memory for DMA/internal allocations
I (219) main_task: Calling app_main()
I (239) BackgroundTask: background_task started
I (239) Board: UUID=7861a53f-5fb7-4e9c-a4b9-f6dd989e0a2f SKU=bread-compact-wifi
I (239) gpio: GPIO[0]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (249) button: IoT Button Version: 4.1.3
I (249) gpio: GPIO[47]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (259) gpio: GPIO[40]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (269) gpio: GPIO[39]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (279) CompactWifiBoard: Install SSD1306 driver
I (279) CompactWifiBoard: SSD1306 driver installed
I (289) CompactWifiBoard: Turning display on
I (389) Display: Power management not supported
I (389) OledDisplay: Initialize LVGL
I (389) LVGL: Starting LVGL task
I (389) OledDisplay: Adding OLED display
I (409) gpio: GPIO[18]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (409) MCP: Add tool: self.lamp.get_state
I (409) MCP: Add tool: self.lamp.turn_on
I (419) MCP: Add tool: self.lamp.turn_off
I (419) Application: STATE: starting
I (429) NoAudioCodec: Simplex channels created
I (429) Application: Audio processor not detected, setting opus encoder complexity to 0
I (439) AudioCodec: Set input enable to true
I (439) AudioCodec: Set output enable to true
I (439) AudioCodec: Audio codec started
I (449) pp: pp rom version: e7ae62f
I (449) net80211: net80211 rom version: e7ae62f
I (469) wifi:wifi driver task: 3fcdb20c, prio:23, stack:6144, core=0
I (469) wifi:wifi firmware version: 79fa3f41ba
I (469) wifi:wifi certification version: v7.0
I (469) wifi:config NVS flash: disabled
I (469) wifi:config nano formatting: enabled
I (479) wifi:Init data frame dynamic rx buffer num: 8
I (479) wifi:Init dynamic rx mgmt buffer num: 5
I (489) wifi:Init management short buffer num: 32
I (489) wifi:Init dynamic tx buffer num: 32
I (489) wifi:Init static tx FG buffer num: 2
I (499) wifi:Init static rx buffer size: 1600
I (499) wifi:Init static rx buffer num: 6
I (509) wifi:Init dynamic rx buffer num: 8
I (509) wifi_init: rx ba win: 6
I (509) wifi_init: accept mbox: 6
I (519) wifi_init: tcpip mbox: 32
I (519) wifi_init: udp mbox: 6
I (519) wifi_init: tcp mbox: 6
I (519) wifi_init: tcp tx win: 5760
I (529) wifi_init: tcp rx win: 5760
I (529) wifi_init: tcp mss: 1440
I (529) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11
I (589) phy_init: Saving new calibration data due to checksum failure or outdated calibration data, mode(0)
I (609) wifi:mode : sta (b4:3a:45:a1:e4:64)
I (609) wifi:enable tsf
I (3019) wifi: Found AP: TPMLPLINK, BSSID: 68:77:24:b5:c4:cf, RSSI: -22, Channel: 1, Authmode: 4
I (3019) wifi: Found AP: TPMLPLINK, BSSID: f4:84:8d:1a:28:b3, RSSI: -32, Channel: 1, Authmode: 4
W (3029) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2
I (3379) wifi:new:<1,1>, old:<1,0>, ap:<255,255>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (3379) wifi:state: init -> auth (0xb0)
I (3379) wifi:state: auth -> assoc (0x0)
I (3389) wifi:state: assoc -> run (0x10)
I (3409) wifi:connected with TPMLPLINK, aid = 2, channel 1, 40U, bssid = f4:84:8d:1a:28:b3
I (3409) wifi:security: WPA2-PSK, phy: bgn, rssi: -32
I (3409) wifi:pm start, type: 1

I (3409) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us
I (3419) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000
I (3479) wifi:<ba-add>idx:0 (ifx:0, f4:84:8d:1a:28:b3), tid:0, ssn:0, winSize:64
I (3499) wifi:AP's beacon interval = 102400 us, DTIM period = 1
I (4539) wifi: Got IP: *************
I (4539) esp_netif_handlers: sta ip: *************, mask: *************, gw: ***********
I (4559) Application: STATE: activating
I (4559) Ota: Current version: 1.7.6
I (4559) Ota: === OTA Request ===
I (4559) Ota: URL: https://api.tenclass.net/xiaozhi/ota/
I (4559) Ota: Method: POST
I (4559) Ota: Request data: 
I (4569) Ota: HTTP Headers:
I (4569) Ota:   Activation-Version: 1
I (4569) Ota:   Device-Id: b4:3a:45:a1:e4:64
I (4579) Ota:   Client-Id: 7861a53f-5fb7-4e9c-a4b9-f6dd989e0a2f
I (4579) Ota:   User-Agent: bread-compact-wifi/1.7.6
I (4589) Ota:   Accept-Language: zh-CN
I (4589) Ota:   Content-Type: application/json
I (4589) Ota: === End OTA Request ===
I (4599) EspHttp: Opening HTTP connection to https://api.tenclass.net/xiaozhi/ota/
I (4759) esp-x509-crt-bundle: Certificate validated
I (5419) Ota: === HTTP Response Headers ===
I (5419) Ota: Status Code: 200
I (5419) Ota: === End HTTP Response Headers ===
I (5419) Ota: === Raw OTA Response ===
I (5419) Ota: Response data length: zu bytes
I (5429) Ota: Response data: {"mqtt":{"endpoint":"mqtt.xiaozhi.me","client_id":"GID_test@@@b4_3a_45_a1_e4_64@@@7861a53f-5fb7-4e9c-a4b9-f6dd989e0a2f","username":"********************************","password":"1LfM4n/k904xMCS2NweOJPXv1xP6TA5Qvb4fLpkKY7g=","publish_topic":"device-server","subscribe_topic":"null"},"websocket":{"url":"wss://api.tenclass.net/xiaozhi/v1/","token":"test-token"},"server_time":{"timestamp":1751198448922,"timezone_offset":480},"firmware":{"version":"1.7.6","url":""}}
I (5469) Ota: === End Raw OTA Response ===
I (5469) Ota: === JSON Parsing Process ===
I (5479) Ota: Found firmware section
I (5479) Ota: Firmware version from server: 1.7.6
I (5479) Ota: Firmware URL from server: 
I (5489) Ota: No new version available (current: 1.7.6, server: 1.7.6)
I (5489) Ota: No activation section found
I (5499) Ota: No activation challenge found
I (5499) Ota: Found MQTT section, parsing configuration...
I (5509) Ota: MQTT config: endpoint = mqtt.xiaozhi.me
I (5509) Ota: MQTT config: client_id = GID_test@@@b4_3a_45_a1_e4_64@@@7861a53f-5fb7-4e9c-a4b9-f6dd989e0a2f
I (5519) Ota: MQTT config: username = ********************************
I (5539) Ota: MQTT config: password = 1LfM4n/k904xMCS2NweOJPXv1xP6TA5Qvb4fLpkKY7g=
I (5539) Ota: MQTT config: publish_topic = device-server
I (5539) Ota: MQTT config: subscribe_topic = null
I (5539) Ota: MQTT configuration saved successfully
I (5549) Ota: Found WebSocket section, parsing configuration...
I (5549) Ota: WebSocket config: url = wss://api.tenclass.net/xiaozhi/v1/
I (5569) Ota: WebSocket config: token = test-token
I (5569) Ota: WebSocket configuration saved successfully
I (5569) Ota: No server_time section found
I (5569) Ota: === End JSON Parsing Process ===
I (5579) Ota: === OTA Check Result Summary ===
I (5579) Ota: Current version: 1.7.6
I (5589) Ota: Has new version: NO
I (5589) Ota: Has MQTT config: YES
I (5589) Ota: Has WebSocket config: YES
I (5599) Ota: Has activation code: NO
I (5599) Ota: Has activation challenge: NO
I (5599) Ota: Has server time: NO
I (5609) Ota: === End OTA Check Result Summary ===
I (5609) Ota: Running partition: ota_0
I (5609) MCP: Add tool: self.get_device_status
I (5619) MCP: Add tool: self.audio_speaker.set_volume
I (5619) MQTT: Connecting to endpoint mqtt.xiaozhi.me
I (5809) esp-x509-crt-bundle: Certificate validated
I (6259) MQTT: Connected to endpoint
I (6259) MODEL_LOADER: The storage free size is 21952 KB
I (6259) MODEL_LOADER: The partition size is 960 KB
I (6259) MODEL_LOADER: Successfully load srmodels
I (6269) AfeWakeWord: Model 0: wn9_nihaoxiaozhi_tts
I (6269) AFE_CONFIG: Set WakeNet Model: wn9_nihaoxiaozhi_tts
MC Quantized wakenet9: wakenet9l_tts1h8_你好小智_3_0.631_0.635, tigger:v4, mode:0, p:0, (Jun 16 2025 16:18:15)
I (6319) AFE: AFE Version: (1MIC_V250121)
I (6319) AFE: Input PCM Config: total 1 channels(1 microphone, 0 playback), sample rate:16000
I (6329) AFE: AFE Pipeline: [input] ->  -> |VAD(WebRTC)| -> |WakeNet(wn9_nihaoxiaozhi_tts,)| -> [output]
I (6339) AfeWakeWord: Audio detection task started, feed size: 512 fetch size: 512
I (6349) Application: STATE: idle
I (6379) SystemInfo: free sram: 101231 minimal sram: 100667
I (6409) Application: Resampling audio from 16000 to 24000
I (6409) OpusResampler: Resampler configured with input sample rate 16000 and output sample rate 24000
I (12619) Application: STATE: connecting
I (12729) MQTT: Session ID: 863c5c2b
I (12739) wifi:Set ps type: 0, coexist: 0

I (12739) Application: Wake word detected: 你好小智
I (13139) AfeWakeWord: Encode wake word opus 66 packets in 521 ms
I (13149) Application: STATE: listening
I (13229) Application: STATE: speaking
I (13229) Application: >> Hi, 小智
I (13589) Application: << 嗨，是你啊，咋了？
I (16179) Application: STATE: listening
I (25449) SystemInfo: free sram: 98155 minimal sram: 96291
