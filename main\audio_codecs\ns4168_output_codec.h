#ifndef _NS4168_OUTPUT_CODEC_H
#define _NS4168_OUTPUT_CODEC_H

#include "audio_codec.h"

#include <driver/gpio.h>
#include <driver/i2s_std.h>

/**
 * NS4168功放音频编解码器
 * 仅支持音频输出，使用I2S接口
 * 适用于只需要音频播放的应用场景
 */
class Ns4168OutputCodec : public AudioCodec {
private:
    gpio_num_t ctrl_pin_;  // NS4168功放控制引脚

    // 延迟I2S初始化的参数
    gpio_num_t bclk_pin_;
    gpio_num_t ws_pin_;
    gpio_num_t dout_pin_;
    bool i2s_initialized_;

    void CreateI2sOutputChannel(gpio_num_t bclk, gpio_num_t ws, gpio_num_t dout);
    void EnsureI2sInitialized();  // 确保I2S已初始化
    void ClearAudioBuffers();     // 清理音频缓冲区

    virtual int Read(int16_t* dest, int samples) override;
    virtual int Write(const int16_t* data, int samples) override;

public:
    Ns4168OutputCodec(int output_sample_rate,
        gpio_num_t spk_bclk, gpio_num_t spk_ws, gpio_num_t spk_dout, gpio_num_t spk_en);
    virtual ~Ns4168OutputCodec();

    virtual void Start() override;
    virtual void SetOutputVolume(int volume) override;
    virtual void EnableInput(bool enable) override;
    virtual void EnableOutput(bool enable) override;
};

#endif // _NS4168_OUTPUT_CODEC_H
