




























ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x29 (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x490
load:0x403c8700,len:0x4
load:0x403c8704,len:0xb08
load:0x403cb700,len:0x2bb4
entry 0x403c88f4
I (40) octal_psram: vendor id    : 0x0d (AP)
I (40) octal_psram: dev id       : 0x02 (generation 3)
I (40) octal_psram: density      : 0x03 (64 Mbit)
I (42) octal_psram: good-die     : 0x01 (Pass)
I (46) octal_psram: Latency      : 0x01 (Fixed)
I (50) octal_psram: VCC          : 0x01 (3V)
I (54) octal_psram: SRF          : 0x01 (Fast Refresh)
I (59) octal_psram: BurstType    : 0x01 (Hybrid Wrap)
I (64) octal_psram: BurstLen     : 0x01 (32 Byte)
I (68) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)
I (73) octal_psram: DriveStrength: 0x00 (1/1)
I (78) MSPI Timing: PSRAM timing tuning index: 4
I (82) esp_psram: Found 8MB PSRAM device
I (86) esp_psram: Speed: 80MHz
I (88) cpu_start: Multicore app
I (103) cpu_start: Pro cpu start user code
I (103) cpu_start: cpu freq: 240000000 Hz
I (103) app_init: Application information:
I (103) app_init: Project name:     xiaozhi
I (107) app_init: App version:      1.7.6
I (111) app_init: Compile time:     Jun 29 2025 19:42:34
I (116) app_init: ELF file SHA256:  ef09a474c...
I (120) app_init: ESP-IDF:          v5.4.1-dirty
I (125) efuse_init: Min chip rev:     v0.0
I (129) efuse_init: Max chip rev:     v0.99 
I (133) efuse_init: Chip rev:         v0.2
I (136) heap_init: Initializing. RAM available for dynamic allocation:
I (143) heap_init: At 3FCAD350 len 0003C3C0 (240 KiB): RAM
I (148) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM
I (153) heap_init: At 600FE11C len 00001EBC (7 KiB): RTCRAM
I (158) esp_psram: Adding pool of 8192K of PSRAM memory to heap allocator
I (166) spi_flash: detected chip: generic
I (168) spi_flash: flash io: dio
I (173) sleep_gpio: Configure to isolate all GPIO pins in sleep state
I (178) sleep_gpio: Enable automatic switching of GPIO sleep configuration
I (185) main_task: Started on CPU0
I (215) esp_psram: Reserving pool of 64K of internal memory for DMA/internal allocations
I (215) main_task: Calling app_main()
I (235) BackgroundTask: background_task started
I (235) Board: UUID=e65fd817-8fd9-4f37-a7ae-ce5226e2401c SKU=bread-compact-wifi
I (235) gpio: GPIO[0]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (245) button: IoT Button Version: 4.1.3
I (245) gpio: GPIO[47]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (255) gpio: GPIO[40]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (265) gpio: GPIO[39]| InputEn: 1| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 
I (275) CompactWifiBoard: Install SSD1306 driver
I (275) CompactWifiBoard: SSD1306 driver installed
I (285) CompactWifiBoard: Turning display on
I (385) Display: Power management not supported
I (385) OledDisplay: Initialize LVGL
I (385) LVGL: Starting LVGL task
I (385) OledDisplay: Adding OLED display
I (405) gpio: GPIO[18]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 
I (405) MCP: Add tool: self.lamp.get_state
I (405) MCP: Add tool: self.lamp.turn_on
I (415) MCP: Add tool: self.lamp.turn_off
I (415) Application: STATE: starting
I (425) NoAudioCodec: Simplex channels created
I (425) Application: Audio processor not detected, setting opus encoder complexity to 0
I (435) AudioCodec: Set input enable to true
I (435) AudioCodec: Set output enable to true
I (435) AudioCodec: Audio codec started
I (445) pp: pp rom version: e7ae62f
I (445) net80211: net80211 rom version: e7ae62f
I (465) wifi:wifi driver task: 3fcdb22c, prio:23, stack:6144, core=0
I (465) wifi:wifi firmware version: 79fa3f41ba
I (465) wifi:wifi certification version: v7.0
I (465) wifi:config NVS flash: disabled
I (465) wifi:config nano formatting: enabled
I (475) wifi:Init data frame dynamic rx buffer num: 8
I (475) wifi:Init dynamic rx mgmt buffer num: 5
I (485) wifi:Init management short buffer num: 32
I (485) wifi:Init dynamic tx buffer num: 32
I (485) wifi:Init static tx FG buffer num: 2
I (495) wifi:Init static rx buffer size: 1600
I (495) wifi:Init static rx buffer num: 6
I (505) wifi:Init dynamic rx buffer num: 8
I (505) wifi_init: rx ba win: 6
I (505) wifi_init: accept mbox: 6
I (515) wifi_init: tcpip mbox: 32
I (515) wifi_init: udp mbox: 6
I (515) wifi_init: tcp mbox: 6
I (515) wifi_init: tcp tx win: 5760
I (525) wifi_init: tcp rx win: 5760
I (525) wifi_init: tcp mss: 1440
I (525) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11
I (565) wifi:mode : sta (b4:3a:45:a1:e4:64)
I (565) wifi:enable tsf
I (2975) wifi: Found AP: TPMLPLINK, BSSID: f4:84:8d:1a:28:b3, RSSI: -29, Channel: 1, Authmode: 4
I (2975) wifi: Found AP: TPMLPLINK, BSSID: 68:77:24:b5:c4:cf, RSSI: -36, Channel: 1, Authmode: 4
W (2985) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2
I (3335) wifi:new:<1,1>, old:<1,0>, ap:<255,255>, sta:<1,1>, prof:1, snd_ch_cfg:0x0
I (3335) wifi:state: init -> auth (0xb0)
I (3335) wifi:state: auth -> assoc (0x0)
I (3345) wifi:state: assoc -> run (0x10)
I (3355) wifi:connected with TPMLPLINK, aid = 2, channel 1, 40U, bssid = f4:84:8d:1a:28:b3
I (3355) wifi:security: WPA2-PSK, phy: bgn, rssi: -30
I (3365) wifi:pm start, type: 1

I (3365) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us
I (3375) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000
I (3385) wifi:AP's beacon interval = 102400 us, DTIM period = 1
I (4915) wifi:<ba-add>idx:0 (ifx:0, f4:84:8d:1a:28:b3), tid:0, ssn:0, winSize:64
I (6035) wifi: Got IP: ***********03
I (6035) esp_netif_handlers: sta ip: ***********03, mask: *************, gw: ***********
I (6055) Application: STATE: activating
I (6055) Ota: Current version: 1.7.6
I (6055) Ota: === OTA Request ===
I (6055) Ota: URL: http://*************:8002/xiaozhi/ota/
I (6055) Ota: Method: POST
I (6055) Ota: Request data: 
I (6065) Ota: HTTP Headers:
I (6065) Ota:   Activation-Version: 1
I (6065) Ota:   Device-Id: b4:3a:45:a1:e4:64
I (6075) Ota:   Client-Id: e65fd817-8fd9-4f37-a7ae-ce5226e2401c
I (6075) Ota:   User-Agent: bread-compact-wifi/1.7.6
I (6085) Ota:   Accept-Language: zh-CN
I (6085) Ota:   Content-Type: application/json
I (6085) Ota: === End OTA Request ===
I (6095) EspHttp: Opening HTTP connection to http://*************:8002/xiaozhi/ota/
I (6265) Ota: === HTTP Response Headers ===
I (6265) Ota: Status Code: 200
I (6265) Ota: === End HTTP Response Headers ===
I (6265) Ota: === Raw OTA Response ===
I (6265) Ota: Response data length: zu bytes
I (6265) Ota: Response data: {"server_time":{"timestamp":1751197853602,"timeZone":"Asia/Shanghai","timezone_offset":480},"firmware":{"version":"1.7.6","url":""},"websocket":{"url":"ws://*************:8000/xiaozhi/v1/"}}
I (6285) Ota: === End Raw OTA Response ===
I (6295) Ota: === JSON Parsing Process ===
I (6295) Ota: Found firmware section
I (6295) Ota: Firmware version from server: 1.7.6
I (6305) Ota: Firmware URL from server: 
I (6305) Ota: No new version available (current: 1.7.6, server: 1.7.6)
I (6315) Ota: No activation section found
I (6315) Ota: No activation challenge found
I (6325) Ota: No mqtt section found !
I (6325) Ota: Found WebSocket section, parsing configuration...
I (6325) Ota: WebSocket config: url = ws://*************:8000/xiaozhi/v1/
I (6335) Ota: WebSocket configuration saved successfully
I (6345) Ota: No server_time section found
I (6345) Ota: === End JSON Parsing Process ===
I (6345) Ota: === OTA Check Result Summary ===
I (6355) Ota: Current version: 1.7.6
I (6355) Ota: Has new version: NO
I (6355) Ota: Has MQTT config: NO
I (6365) Ota: Has WebSocket config: YES
I (6365) Ota: Has activation code: NO
I (6365) Ota: Has activation challenge: NO
I (6375) Ota: Has server time: NO
I (6375) Ota: === End OTA Check Result Summary ===
I (6385) Ota: Running partition: ota_0
I (6385) MCP: Add tool: self.get_device_status
I (6385) MCP: Add tool: self.audio_speaker.set_volume
I (6395) MODEL_LOADER: The storage free size is 21952 KB
I (6395) MODEL_LOADER: The partition size is 960 KB
I (6405) MODEL_LOADER: Successfully load srmodels
I (6405) AfeWakeWord: Model 0: wn9_nihaoxiaozhi_tts
I (6415) AFE_CONFIG: Set WakeNet Model: wn9_nihaoxiaozhi_tts
MC Quantized wakenet9: wakenet9l_tts1h8_你好小智_3_0.631_0.635, tigger:v4, mode:0, p:0, (Jun 16 2025 16:18:15)
I (6465) AFE: AFE Version: (1MIC_V250121)
I (6465) AFE: Input PCM Config: total 1 channels(1 microphone, 0 playback), sample rate:16000
I (6475) AFE: AFE Pipeline: [input] ->  -> |VAD(WebRTC)| -> |WakeNet(wn9_nihaoxiaozhi_tts,)| -> [output]
I (6485) AfeWakeWord: Audio detection task started, feed size: 512 fetch size: 512
I (6485) Application: STATE: idle
I (6515) SystemInfo: free sram: 110731 minimal sram: 110203
I (6525) Application: Resampling audio from 16000 to 24000
I (6525) OpusResampler: Resampler configured with input sample rate 16000 and output sample rate 24000
I (9295) Application: STATE: connecting
I (9305) WS: Connecting to websocket server: ws://*************:8000/xiaozhi/v1/ with version: 1
I (9305) WebSocket: Connecting to ws://*************:8000/xiaozhi/v1/
I (9415) WS: Session ID: 254dc3de-6fca-4534-9c16-5117e995124b
I (9415) wifi:Set ps type: 0, coexist: 0

W (9415) Application: Server sample rate 16000 does not match device output sample rate 24000, resampling may cause distortion
I (9425) Application: Wake word detected: 你好小智
I (9455) WS: Session ID: 254dc3de-6fca-4534-9c16-5117e995124b
I (9795) AfeWakeWord: Encode wake word opus 66 packets in 493 ms
I (9795) Application: STATE: listening
I (9875) Application: >> 嘿，你好呀
I (9885) Application: STATE: speaking
I (19445) SystemInfo: free sram: 105467 minimal sram: 102439
I (29445) SystemInfo: free sram: 105423 minimal sram: 102439
