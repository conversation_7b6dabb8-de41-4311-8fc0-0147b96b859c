# NS4168 音频功放激活指南

## 概述

本文档提供了在 xiaozhi-esp32 项目中激活和使用 NS4168 数字音频功放的详细步骤。NS4168 是一个高性能的 Class-D 音频功放，支持 I2S 数字音频输入，适用于各种音频应用场景。

## 硬件连接

### 1. 电源连接

| NS4168 引脚 | 连接说明 |
|-------------|----------|
| VDD         | 连接到 ESP32 的 3.3V 或 5V 电源 |
| GND         | 连接到 ESP32 的 GND |

### 2. I2S 信号连接

| NS4168 引脚 | ESP32 引脚 | 功能说明 |
|-------------|------------|----------|
| BCLK        | GPIO_15    | I2S 位时钟 |
| LRCLK       | GPIO_16    | I2S 帧时钟 |
| SDATA       | GPIO_7     | I2S 串行数据输入 |
| CTRL        | GPIO_13    | 控制引脚（可选）|

### 3. 音频输出连接

| NS4168 引脚 | 连接说明 |
|-------------|----------|
| VoP         | 连接到扬声器正极 |
| VoN         | 连接到扬声器负极 |

## 软件配置

### 1. 启用编译选项

在 `menuconfig` 中启用 NS4168 支持：

```
Xiaozhi Assistant → 使用 NS4168 数字音频功放芯片 → Y
```

或在 `sdkconfig` 中添加：

```
CONFIG_USE_NS4168_AUDIO_AMPLIFIER=y
```

### 2. 代码配置

在 board 文件中添加以下代码：

```cpp
#include "ns4168_audio_codec.h"

// 定义 GPIO 引脚
#define I2S_BCLK_PIN    GPIO_NUM_15
#define I2S_WS_PIN      GPIO_NUM_16  
#define I2S_DIN_PIN     GPIO_NUM_7
#define NS4168_CTRL_PIN GPIO_NUM_13

// 创建 NS4168 音频功放实例
Ns4168AudioCodec audio_codec(
    16000,              // 输入采样率
    16000,              // 输出采样率
    I2S_BCLK_PIN,       // BCLK 引脚
    I2S_WS_PIN,         // LRCLK 引脚
    I2S_DIN_PIN,        // SDATA 输入引脚
    I2S_GPIO_UNUSED,    // 不需要输出引脚
    NS4168_CTRL_PIN,    // 控制引脚（可选）
    false               // 选择右声道
);

// 在 board 文件中使用
virtual AudioCodec* GetAudioCodec() override {
    return &audio_codec;
}
```

### 3. 测试音播放

在 board 构造函数中添加测试音播放：

```cpp
CompactWifiBoard() {
    // ... 其他初始化代码 ...
    
    // 播放440Hz测试音，持续3秒
    PlayTestTone();
}

// 测试音播放方法
void PlayTestTone() {
#ifdef CONFIG_USE_NS4168_AUDIO_AMPLIFIER
    auto codec = GetAudioCodec();
    if (!codec) return;

    // 启用输出
    codec->EnableOutput(true);
    
    // 生成440Hz正弦波
    const int sample_rate = 16000;  // 采样率
    const int duration = 3;         // 持续时间（秒）
    const int samples = sample_rate * duration;
    const float frequency = 440.0f;  // 440Hz
    const float amplitude = 0.5f;     // 音量
    
    // 创建音频数据缓冲区
    std::vector<int16_t> buffer(samples);
    for (int i = 0; i < samples; i++) {
        float t = (float)i / sample_rate;
        buffer[i] = (int16_t)(amplitude * 32767.0f * sinf(2.0f * M_PI * frequency * t));
    }
    
    // 播放测试音
    codec->OutputData(buffer);
    
    // 禁用输出
    codec->EnableOutput(false);
#endif
}
```

## 验证步骤

1. **编译和烧录**
   - 使用 `idf.py build` 编译项目
   - 使用 `idf.py flash` 烧录固件

2. **开机测试**
   - 上电后应听到 440Hz 测试音
   - 测试音持续 3 秒
   - 声音应清晰无失真

3. **功能验证**
   - 测试音播放正常
   - 无异常噪声
   - 音量适中

## 故障排除

### 1. 无测试音输出

检查项目：
- 确认 I2S 引脚连接正确（BCLK=GPIO_15, LRCLK=GPIO_16, SDATA=GPIO_7）
- 检查采样率配置是否在支持范围内
- 确认音频功放已启用：`EnableOutput(true)`
- 检查控制引脚是否正确配置（右声道需要高电平）

### 2. 声音异常

可能原因：
- 采样率不匹配
- 声道选择错误
- 电源电压不稳定
- 扬声器阻抗不匹配

### 3. 常见问题

1. **无声输出**
   - 检查电源电压（3.3V/5V）
   - 确认控制引脚电平正确
   - 验证I2S信号是否正常

2. **声音失真**
   - 检查采样率设置
   - 确认电源电压稳定
   - 检查扬声器连接

3. **电流声**
   - 检查电源去耦
   - 确认地线连接
   - 检查信号线屏蔽

## 优化建议

1. **电源优化**
   - 使用独立的电源供电
   - 添加足够的去耦电容
   - 保持电源电压稳定

2. **布线优化**
   - 音频输出线尽量短
   - 避免与数字信号线平行
   - 使用屏蔽线或地线隔离

3. **散热考虑**
   - 确保芯片有足够的散热空间
   - 避免长时间大功率输出
   - 监控芯片温度

## 重要说明

1. **采样率限制**
   - 输入输出采样率必须一致
   - 支持范围：8kHz - 96kHz
   - 推荐使用16kHz采样率

2. **声道选择**
   - 左声道：CTRL = 0.9V - 1.15V
   - 右声道：CTRL = 1.5V - VDD
   - 关断模式：CTRL = 0 - 0.4V

3. **功耗管理**
   - 使用控制引脚进行功耗管理
   - 不需要时进入关断模式
   - 注意散热问题

## 参考资源

- [NS4168数据手册](https://www.novosns.com/ns4168)
- [ESP32 I2S文档](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/peripherals/i2s.html)
- [I2S协议说明](https://en.wikipedia.org/wiki/I%C2%B2S) 