CONFIG_COMPILER_OPTIMIZATION_SIZE=y
CONFIG_COMPILER_CXX_EXCEPTIONS=y
CONFIG_COMPILER_CXX_EXCEPTIONS_EMG_POOL_SIZE=1024

CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF=y
CONFIG_BOOTLOADER_LOG_LEVEL_NONE=y
CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS=y
CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE=y

CONFIG_HTTPD_MAX_REQ_HDR_LEN=2048
CONFIG_HTTPD_MAX_URI_LEN=2048

CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions/v1/16m.csv"
CONFIG_PARTITION_TABLE_OFFSET=0x8000

CONFIG_ESP_TASK_WDT_TIMEOUT_S=10
CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=y
CONFIG_FREERTOS_USE_STATS_FORMATTING_FUNCTIONS=y

CONFIG_ESP_MAIN_TASK_STACK_SIZE=8192
CONFIG_MBEDTLS_DYNAMIC_BUFFER=y
CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE=n
CONFIG_ESP_WIFI_IRAM_OPT=n
CONFIG_ESP_WIFI_RX_IRAM_OPT=n
CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUFFER=y

# These entries are copied from ESP-HI (ESP32C3) to reduce memory usage
CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM=6
CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM=8
CONFIG_NEWLIB_NANO_FORMAT=y
CONFIG_ESP_WIFI_ENTERPRISE_SUPPORT=n

CONFIG_CODEC_I2C_BACKWARD_COMPATIBLE=n

# Fix ML307 FIFO Overflow
CONFIG_UART_ISR_IN_IRAM=y

# Development debug features (disable in production)
CONFIG_DEVELOP_DEBUG=y

# LVGL 9.2.2

CONFIG_LV_OS_NONE=y
CONFIG_LV_USE_OS=0
CONFIG_LV_USE_CLIB_MALLOC=y
CONFIG_LV_USE_CLIB_STRING=y
CONFIG_LV_USE_CLIB_SPRINTF=y
CONFIG_LV_USE_IMGFONT=y

# Use compressed font
CONFIG_LV_FONT_FMT_TXT_LARGE=y
CONFIG_LV_USE_FONT_COMPRESSED=y
CONFIG_LV_USE_FONT_PLACEHOLDER=y

# Disable extra widgets to save flash size
CONFIG_LV_USE_ANIMIMG=n
CONFIG_LV_USE_CALENDAR=n
CONFIG_LV_USE_CALENDAR_HEADER_ARROW=n
CONFIG_LV_USE_CALENDAR_HEADER_DROPDOWN=n
CONFIG_LV_USE_CHART=n
CONFIG_LV_USE_KEYBOARD=n
CONFIG_LV_USE_LED=n
CONFIG_LV_USE_LIST=n
CONFIG_LV_USE_MENU=n
CONFIG_LV_USE_MSGBOX=n
CONFIG_LV_USE_SPAN=n
CONFIG_LV_USE_SPINBOX=n
CONFIG_LV_USE_SPINNER=n
CONFIG_LV_USE_TABVIEW=n
CONFIG_LV_USE_TILEVIEW=n
CONFIG_LV_USE_WIN=n

CONFIG_LV_BUILD_EXAMPLES=n

